using System.Text.Json.Serialization;

namespace Bonus.Adapters.LSRetail.Requests;

public class LSReplEcommHierarchyNodeRequest
{
    [JsonPropertyName("replRequest")]
    public required LSReplRequest ReplRequest { get; set; }
}

public class LSReplEcommHierarchyNodeResponse
{
    [JsonPropertyName("ReplEcommHierarchyNodeResult")]
    public required LSReplEcommHierarchyNodeResult Result { get; set; }
}

public class LSReplEcommHierarchyNodeResult
{
    [JsonPropertyName("LastKey")]
    public string LastKey { get; set; } = string.Empty;
    
    [JsonPropertyName("MaxKey")]
    public string MaxKey { get; set; } = string.Empty;
    
    [JsonPropertyName("RecordsRemaining")]
    public int RecordsRemaining { get; set; }
    
    [JsonPropertyName("Nodes")]
    public List<LSReplEcommHierarchyNode> Nodes { get; set; } = [];
}

public class LSReplEcommHierarchyNode
{
    [JsonPropertyName("Id")]
    public required string Id { get; set; }
    
    [JsonPropertyName("Description")]
    public string? Description { get; set; }
    
    [JsonPropertyName("HierarchyCode")]
    public string? HierarchyCode { get; set; }
    
    [JsonPropertyName("ParentNode")]
    public string? ParentNode { get; set; }
    
    [JsonPropertyName("ImageId")]
    public string? ImageId { get; set; }
    
    [JsonPropertyName("Indentation")]
    public int Indentation { get; set; }
    
    [JsonPropertyName("ChildrenOrder")]
    public int ChildrenOrder { get; set; }
    
    [JsonPropertyName("PresentationOrder")]
    public int PresentationOrder { get; set; }
    
    [JsonPropertyName("IsDeleted")]
    public bool IsDeleted { get; set; }
}
