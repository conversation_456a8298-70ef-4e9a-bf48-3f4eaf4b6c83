using Bonus.Services.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

//TODO: only for testing, remove!
namespace Bonus.Api.Controllers;

[ApiController]
[AllowAnonymous]
[Route("api/[controller]")]
public class HierarchySyncController(IHierarchySyncService hierarchySyncService) : ControllerBase
{
    /// <summary>
    /// Sync all hierarchy nodes and leafs from LSRetail
    /// </summary>
    [HttpPost("sync-all")]
    public async Task<IActionResult> SyncAllHierarchy(CancellationToken cancellationToken)
    {
        await hierarchySyncService.SyncHierarchyAsync(cancellationToken);
        return Ok(new { message = "Hierarchy sync completed successfully" });
    }
}
