using Bonus.Adapters.LSRetail;
using Bonus.Adapters.LSRetail.Requests;
using Bonus.Core.Data;
using Bonus.Core.Data.Entities;
using Bonus.Core.Services;
using Bonus.Shared.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Bonus.Services.Services;

public interface IHierarchySyncService
{
    Task SyncHierarchyAsync(CancellationToken cancellationToken = default);
}

public class HierarchySyncService(
    ILSRetailAdapter lsRetailAdapter,
    BonusContext dbContext,
    ISystemTime systemTime,
    ILogger<HierarchySyncService> logger) : IHierarchySyncService
{
    private const int BatchSize = 1000;

    public async Task SyncHierarchyAsync(CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting full hierarchy synchronization");

        await SyncNodesAsync(cancellationToken);
        await SyncLeafsAsync(cancellationToken);

        logger.LogInformation("Completed full hierarchy synchronization");
    }

    private async Task SyncNodesAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting hierarchy nodes synchronization");

        await dbContext.Database.ExecuteSqlRawAsync("TRUNCATE TABLE [hierarchy_nodes]");

        var batchResult = await SyncNodesBatchInternalAsync("0", BatchSize, cancellationToken);
        var totalProcessed = batchResult.ProcessedCount;
        var lastKey = batchResult.LastKey;

        logger.LogInformation("Processed nodes batch: {ProcessedCount} items, Total: {TotalProcessed}, LastKey: {LastKey}",
            batchResult.ProcessedCount, totalProcessed, lastKey);

        while (lastKey.HasValue() && batchResult.RecordsRemaining > 0)
        {
            batchResult = await SyncNodesBatchInternalAsync(lastKey, BatchSize, cancellationToken);
            totalProcessed += batchResult.ProcessedCount;
            lastKey = batchResult.LastKey;

            logger.LogInformation("Processed nodes batch: {ProcessedCount} items, Total: {TotalProcessed}, LastKey: {LastKey}, RecordsRemaining: {RecordsRemaining}",
                batchResult.ProcessedCount, totalProcessed, lastKey, batchResult.RecordsRemaining);
        }

        logger.LogInformation("Completed hierarchy nodes synchronization. Total processed: {TotalProcessed}", totalProcessed);
    }

    private async Task SyncLeafsAsync(CancellationToken cancellationToken)
    {
        logger.LogInformation("Starting hierarchy leafs synchronization");

        await dbContext.Database.ExecuteSqlRawAsync("TRUNCATE TABLE [hierarchy_leafs]");

        var batchResult = await SyncLeafsBatchInternalAsync("0", BatchSize, cancellationToken);
        var totalProcessed = batchResult.ProcessedCount;
        var lastKey = batchResult.LastKey;

        logger.LogInformation("Processed leafs batch: {ProcessedCount} items, Total: {TotalProcessed}, LastKey: {LastKey}",
            batchResult.ProcessedCount, totalProcessed, lastKey);

        while (lastKey.HasValue() && batchResult.RecordsRemaining > 0)
        {
            batchResult = await SyncLeafsBatchInternalAsync(lastKey, BatchSize, cancellationToken);
            totalProcessed += batchResult.ProcessedCount;
            lastKey = batchResult.LastKey;
        }

        logger.LogInformation("Completed hierarchy leafs synchronization. Total processed: {TotalProcessed}", totalProcessed);
    }

    private async Task<BatchSyncResult> SyncNodesBatchInternalAsync(string lastKey, int batchSize, CancellationToken cancellationToken)
    {
        var request = new LSReplEcommHierarchyNodeRequest
        {
            ReplRequest = new LSReplRequest
            {
                AppId = "BonusApp",
                StoreId = "01",
                BatchSize = batchSize,
                FullReplication = true,
                LastKey = lastKey,
            }
        };

        var response = await lsRetailAdapter.ReplEcommHierarchyNode(request, cancellationToken);

        if (!response.Success || response.Response?.Result?.Nodes == null)
        {
            logger.LogWarning("No nodes received from LSRetail API. IsSuccess: {Success}", response.Success);
            return new BatchSyncResult { ProcessedCount = 0, LastKey = "", RecordsRemaining = 0 };
        }

        var nodes = response.Response.Result.Nodes;
        var processedCount = 0;
        var syncTime = systemTime.UtcNow;

        foreach (var node in nodes)
        {
            try
            {
                var newNode = CreateNode(node, syncTime);
                dbContext.HierarchyNodes.Add(newNode);
                processedCount++;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to sync hierarchy node {NodeId}: {ErrorMessage}", node.Id, ex.Message);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);

        return new BatchSyncResult
        {
            ProcessedCount = processedCount,
            LastKey = response.Response.Result.LastKey,
            RecordsRemaining = response.Response.Result.RecordsRemaining
        };
    }

    private async Task<BatchSyncResult> SyncLeafsBatchInternalAsync(string lastKey, int batchSize, CancellationToken cancellationToken)
    {
        var request = new LSReplEcommHierarchyLeafRequest
        {
            ReplRequest = new LSReplRequest
            {
                AppId = "BonusApp",
                StoreId = "01",
                BatchSize = batchSize,
                FullReplication = true,
                LastKey = lastKey,
            }
        };

        var response = await lsRetailAdapter.ReplEcommHierarchyLeaf(request, cancellationToken);

        if (!response.Success || response.Response?.Result?.Leafs == null)
        {
            logger.LogWarning("No leafs received from LSRetail API. Success: {Success}", response.Success);
            return new BatchSyncResult { ProcessedCount = 0, LastKey = "", RecordsRemaining = 0 };
        }

        var leafs = response.Response.Result.Leafs;
        var processedCount = 0;
        var syncTime = systemTime.UtcNow;

        foreach (var leaf in leafs)
        {
            try
            {
                var newLeaf = CreateLeaf(leaf, syncTime);
                dbContext.HierarchyLeafs.Add(newLeaf);

                processedCount++;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to sync hierarchy leaf {LeafId}: {ErrorMessage}", leaf.Id, ex.Message);
            }
        }

        await dbContext.SaveChangesAsync(cancellationToken);

        return new BatchSyncResult
        {
            ProcessedCount = processedCount,
            LastKey = response.Response.Result.LastKey,
            RecordsRemaining = response.Response.Result.RecordsRemaining
        };
    }

    private static HierarchyNode CreateNode(LSReplEcommHierarchyNode lsNode, DateTime syncTime)
    {
        return new HierarchyNode
        {
            NodeId = lsNode.Id,
            Description = lsNode.Description,
            HierarchyCode = lsNode.HierarchyCode,
            ParentNode = lsNode.ParentNode,
            ImageId = lsNode.ImageId,
            Indentation = lsNode.Indentation,
            ChildrenOrder = lsNode.ChildrenOrder,
            PresentationOrder = lsNode.PresentationOrder,
            IsDeleted = lsNode.IsDeleted,
            LastSyncTime = syncTime,
            CreatedTime = syncTime
        };
    }

    private static HierarchyLeaf CreateLeaf(LSReplEcommHierarchyLeaf lsLeaf, DateTime syncTime)
    {
        return new HierarchyLeaf
        {
            LeafId = lsLeaf.Id,
            Description = lsLeaf.Description,
            HierarchyCode = lsLeaf.HierarchyCode,
            NodeId = lsLeaf.NodeId,
            ImageId = lsLeaf.ImageId,
            SortOrder = lsLeaf.SortOrder,
            Type = lsLeaf.Type,
            IsActive = lsLeaf.IsActive,
            IsDeleted = lsLeaf.IsDeleted,
            LastSyncTime = syncTime,
            CreatedTime = syncTime
        };
    }

    private class BatchSyncResult
    {
        public int ProcessedCount { get; set; }

        public string LastKey { get; set; } = string.Empty;

        public int RecordsRemaining { get; set; }
    }
}
