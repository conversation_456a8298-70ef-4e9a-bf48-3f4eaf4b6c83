using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class HierarchyLeaf : BaseEntity
{
    public required string LeafId { get; set; }

    public string? Description { get; set; }

    public string? HierarchyCode { get; set; }

    public string? NodeId { get; set; }

    public string? ImageId { get; set; }

    public int SortOrder { get; set; }

    public int Type { get; set; }

    public bool IsActive { get; set; }

    public bool IsDeleted { get; set; }

    public bool IsMemberClub { get; set; }

    public string? ItemUOM { get; set; }

    public string? MemberValue { get; set; }

    public decimal DealPrice { get; set; }

    public decimal Prepayment { get; set; }

    public string? ValidationPeriod { get; set; }

    public bool VendorSourcing { get; set; }

    public DateTime LastSyncTime { get; set; }
}
