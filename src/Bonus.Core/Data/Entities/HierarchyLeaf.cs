using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class HierarchyLeaf : BaseEntity
{
    public required string LeafId { get; set; }

    public string? Description { get; set; }

    public string? HierarchyCode { get; set; }

    public string? NodeId { get; set; }

    public string? ImageId { get; set; }

    public int SortOrder { get; set; }

    public int Type { get; set; }

    public bool IsActive { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime LastSyncTime { get; set; }
}
