using Bonus.Shared.Data.Entities.Base;

namespace Bonus.Core.Data.Entities;

public class HierarchyNode : BaseEntity
{
    public required string NodeId { get; set; }

    public string? Description { get; set; }

    public string? HierarchyCode { get; set; }

    public string? ParentNode { get; set; }

    public string? ImageId { get; set; }

    public int Indentation { get; set; }

    public int ChildrenOrder { get; set; }

    public int PresentationOrder { get; set; }

    public bool IsDeleted { get; set; }

    public DateTime LastSyncTime { get; set; }
}
