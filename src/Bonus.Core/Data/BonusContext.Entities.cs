using Bonus.Core.Data.Entities;
using Bonus.Core.Data.Entities.Search;
using Microsoft.EntityFrameworkCore;

namespace Bonus.Core.Data;

public partial class BonusContext
{
    public DbSet<User> Users => Set<User>();

    public DbSet<RefreshToken> RefreshTokens => Set<RefreshToken>();

    public DbSet<AuthenticationLog> AuthenticationLogs => Set<AuthenticationLog>();

    public DbSet<Notification> Notifications => Set<Notification>();

    public DbSet<NotificationRecipient> NotificationRecipients => Set<NotificationRecipient>();

    public DbSet<HagarIdSession> HagarIdSessions => Set<HagarIdSession>();

    public DbSet<ProductSearchEmbedding> ProductSearchEmbeddings => Set<ProductSearchEmbedding>();

    public DbSet<ShoppingListItem> ShoppingListItems => Set<ShoppingListItem>();

    public DbSet<ShoppingListShare> ShoppingListShares => Set<ShoppingListShare>();

    public DbSet<Product> Products => Set<Product>();

    public DbSet<HierarchyNode> HierarchyNodes => Set<HierarchyNode>();

    public DbSet<HierarchyLeaf> HierarchyLeafs => Set<HierarchyLeaf>();

    public DbSet<StoreMapping> StoreMappings => Set<StoreMapping>();

    public DbSet<Promotion> Promotions => Set<Promotion>();
}
