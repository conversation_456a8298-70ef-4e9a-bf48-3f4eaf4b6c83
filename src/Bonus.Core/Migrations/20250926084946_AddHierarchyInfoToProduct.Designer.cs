// <auto-generated />
using System;
using Bonus.Core.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Bonus.Core.Migrations
{
    [DbContext(typeof(BonusContext))]
    [Migration("20250926084946_AddHierarchyInfoToProduct")]
    partial class AddHierarchyInfoToProduct
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.5")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Bonus.Core.Data.Entities.AuthenticationLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AppVersion")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_version");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("CredentialType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("credential_type");

                    b.Property<string>("CredentialValue")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("credential_value");

                    b.Property<string>("DeviceId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("device_id");

                    b.Property<string>("DeviceManufacturer")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("device_manufacturer");

                    b.Property<string>("DeviceModelName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("device_model_name");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("error_message");

                    b.Property<int?>("HagarIdSessionId")
                        .HasColumnType("int")
                        .HasColumnName("hagar_id_session_id");

                    b.Property<string>("IpAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ip_address");

                    b.Property<bool>("IsSuccess")
                        .HasColumnType("bit")
                        .HasColumnName("is_success");

                    b.Property<string>("OperatingSystem")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("operating_system");

                    b.Property<string>("OperatingSystemVersion")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("operating_system_version");

                    b.Property<int?>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_authentication_logs");

                    b.HasIndex("HagarIdSessionId")
                        .HasDatabaseName("ix_authentication_logs_hagar_id_session_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_authentication_logs_user_id");

                    b.ToTable("authentication_logs", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.HagarIdSession", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<DateTime>("ExpiresAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("expires_at");

                    b.Property<bool>("IsCompleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_completed");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("session_id");

                    b.HasKey("Id")
                        .HasName("pk_hagar_id_sessions");

                    b.ToTable("hagar_id_sessions", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.HierarchyLeaf", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("HierarchyCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("hierarchy_code");

                    b.Property<string>("ImageId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("image_id");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime>("LastSyncTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("last_sync_time");

                    b.Property<string>("LeafId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("leaf_id");

                    b.Property<string>("NodeId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("node_id");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasColumnName("type");

                    b.HasKey("Id")
                        .HasName("pk_hierarchy_leafs");

                    b.HasIndex("LeafId")
                        .IsUnique()
                        .HasDatabaseName("ix_hierarchy_leafs_leaf_id");

                    b.ToTable("hierarchy_leafs", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.HierarchyNode", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<int>("ChildrenOrder")
                        .HasColumnType("int")
                        .HasColumnName("children_order");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("HierarchyCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("hierarchy_code");

                    b.Property<string>("ImageId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("image_id");

                    b.Property<int>("Indentation")
                        .HasColumnType("int")
                        .HasColumnName("indentation");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime>("LastSyncTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("last_sync_time");

                    b.Property<string>("NodeId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("node_id");

                    b.Property<string>("ParentNode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("parent_node");

                    b.Property<int>("PresentationOrder")
                        .HasColumnType("int")
                        .HasColumnName("presentation_order");

                    b.HasKey("Id")
                        .HasName("pk_hierarchy_nodes");

                    b.HasIndex("NodeId")
                        .IsUnique()
                        .HasDatabaseName("ix_hierarchy_nodes_node_id");

                    b.ToTable("hierarchy_nodes", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Notification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<DateTime?>("SentAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("sent_at");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)")
                        .HasColumnName("title");

                    b.HasKey("Id")
                        .HasName("pk_notifications");

                    b.ToTable("notifications", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.NotificationRecipient", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<int>("NotificationId")
                        .HasColumnType("int")
                        .HasColumnName("notification_id");

                    b.Property<DateTime?>("ReadTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("read_time");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.HasKey("Id")
                        .HasName("pk_notification_recipients");

                    b.HasIndex("NotificationId")
                        .HasDatabaseName("ix_notification_recipients_notification_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_notification_recipients_user_id");

                    b.ToTable("notification_recipients", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Product", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("details");

                    b.Property<string>("HierarchyNodeId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("hierarchy_node_id");

                    b.Property<string>("ImageId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("image_id");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ItemCategoryCode")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("item_category_code");

                    b.Property<string>("ItemId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("item_id");

                    b.Property<DateTime>("LastSyncTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("last_sync_time");

                    b.Property<string>("SalesUnitOfMeasure")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("sales_unit_of_measure");

                    b.Property<string>("SortOrder")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("sort_order");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("unit_price");

                    b.HasKey("Id")
                        .HasName("pk_products");

                    b.HasIndex("ItemId")
                        .IsUnique()
                        .HasDatabaseName("ix_products_item_id");

                    b.ToTable("products", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Promotion", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Base64Image")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("base64image");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("LSRetailStoreId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ls_retail_store_id");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title");

                    b.Property<DateTime>("ValidFrom")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_from");

                    b.Property<DateTime?>("ValidTo")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_to");

                    b.HasKey("Id")
                        .HasName("pk_promotions");

                    b.ToTable("promotions", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.RefreshToken", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<int>("AuthenticationLogId")
                        .HasColumnType("int")
                        .HasColumnName("authentication_log_id");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("token");

                    b.Property<DateTime?>("UsedOn")
                        .HasColumnType("datetime2")
                        .HasColumnName("used_on");

                    b.Property<int>("UserId")
                        .HasColumnType("int")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("ValidUntil")
                        .HasColumnType("datetime2")
                        .HasColumnName("valid_until");

                    b.HasKey("Id")
                        .HasName("pk_refresh_tokens");

                    b.HasIndex("AuthenticationLogId")
                        .HasDatabaseName("ix_refresh_tokens_authentication_log_id");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_refresh_tokens_user_id");

                    b.ToTable("refresh_tokens", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Search.ProductSearchEmbedding", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<byte[]>("Embedding")
                        .IsRequired()
                        .HasColumnType("varbinary(max)")
                        .HasColumnName("embedding");

                    b.Property<string>("EmbeddingSource")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("embedding_source");

                    b.Property<string>("ItemId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("item_id");

                    b.HasKey("Id")
                        .HasName("pk_product_search_embeddings");

                    b.HasIndex("ItemId")
                        .IsUnique()
                        .HasDatabaseName("ix_product_search_embeddings_item_id");

                    b.ToTable("product_search_embeddings", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.ShoppingListItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<bool>("Completed")
                        .HasColumnType("bit")
                        .HasColumnName("completed");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("ItemDescription")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("item_description");

                    b.Property<string>("ShoppingListId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("shopping_list_id");

                    b.HasKey("Id")
                        .HasName("pk_shopping_list_items");

                    b.HasIndex("ShoppingListId", "ItemDescription")
                        .HasDatabaseName("perf_shopping_list_items");

                    b.ToTable("shopping_list_items", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.ShoppingListShare", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("LinkedCardId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("linked_card_id");

                    b.Property<string>("ShareId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("share_id");

                    b.Property<string>("ShoppingListId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("shopping_list_id");

                    b.HasKey("Id")
                        .HasName("pk_shopping_list_shares");

                    b.HasIndex("ShareId", "ShoppingListId", "LinkedCardId")
                        .HasDatabaseName("perf_shopping_list_shares");

                    b.ToTable("shopping_list_shares", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.StoreMapping", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("EasyShopStoreId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("easy_shop_store_id");

                    b.Property<string>("LSRetailStoreId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ls_retail_store_id");

                    b.HasKey("Id")
                        .HasName("pk_store_mappings");

                    b.ToTable("store_mappings", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.User", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AccountId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("account_id");

                    b.Property<DateTime?>("ArchivedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("archived_time");

                    b.Property<string>("AuthenticationId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("authentication_id");

                    b.Property<string>("CardId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("card_id");

                    b.Property<DateTime>("CreatedTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("created_time");

                    b.Property<string>("MemberContactId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("member_contact_id");

                    b.Property<string>("Ssn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ssn");

                    b.Property<string>("UserExternalNotificationId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("user_external_notification_id");

                    b.HasKey("Id")
                        .HasName("pk_users");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.AuthenticationLog", b =>
                {
                    b.HasOne("Bonus.Core.Data.Entities.HagarIdSession", "HagarIdSession")
                        .WithMany()
                        .HasForeignKey("HagarIdSessionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_authentication_logs_hagar_id_sessions_hagar_id_session_id");

                    b.HasOne("Bonus.Core.Data.Entities.User", "User")
                        .WithMany("AuthenticationLogs")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .HasConstraintName("fk_authentication_logs_users_user_id");

                    b.Navigation("HagarIdSession");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.NotificationRecipient", b =>
                {
                    b.HasOne("Bonus.Core.Data.Entities.Notification", "Notification")
                        .WithMany("Recipients")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_notification_recipients_notifications_notification_id");

                    b.HasOne("Bonus.Core.Data.Entities.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_notification_recipients_users_user_id");

                    b.Navigation("Notification");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.RefreshToken", b =>
                {
                    b.HasOne("Bonus.Core.Data.Entities.AuthenticationLog", "AuthenticationLog")
                        .WithMany()
                        .HasForeignKey("AuthenticationLogId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_refresh_tokens_authentication_logs_authentication_log_id");

                    b.HasOne("Bonus.Core.Data.Entities.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired()
                        .HasConstraintName("fk_refresh_tokens_users_user_id");

                    b.Navigation("AuthenticationLog");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.Notification", b =>
                {
                    b.Navigation("Recipients");
                });

            modelBuilder.Entity("Bonus.Core.Data.Entities.User", b =>
                {
                    b.Navigation("AuthenticationLogs");

                    b.Navigation("RefreshTokens");
                });
#pragma warning restore 612, 618
        }
    }
}
