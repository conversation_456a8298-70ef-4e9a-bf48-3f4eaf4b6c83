using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Migrations
{
    /// <inheritdoc />
    public partial class AddHierarchyInfoToProduct : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "deal_price",
                table: "hierarchy_leafs");

            migrationBuilder.DropColumn(
                name: "is_member_club",
                table: "hierarchy_leafs");

            migrationBuilder.DropColumn(
                name: "item_uom",
                table: "hierarchy_leafs");

            migrationBuilder.DropColumn(
                name: "member_value",
                table: "hierarchy_leafs");

            migrationBuilder.DropColumn(
                name: "prepayment",
                table: "hierarchy_leafs");

            migrationBuilder.DropColumn(
                name: "validation_period",
                table: "hierarchy_leafs");

            migrationBuilder.DropColumn(
                name: "vendor_sourcing",
                table: "hierarchy_leafs");

            migrationBuilder.AddColumn<string>(
                name: "hierarchy_node_id",
                table: "products",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "image_id",
                table: "products",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "sort_order",
                table: "products",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "hierarchy_node_id",
                table: "products");

            migrationBuilder.DropColumn(
                name: "image_id",
                table: "products");

            migrationBuilder.DropColumn(
                name: "sort_order",
                table: "products");

            migrationBuilder.AddColumn<decimal>(
                name: "deal_price",
                table: "hierarchy_leafs",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<bool>(
                name: "is_member_club",
                table: "hierarchy_leafs",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "item_uom",
                table: "hierarchy_leafs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "member_value",
                table: "hierarchy_leafs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "prepayment",
                table: "hierarchy_leafs",
                type: "decimal(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "validation_period",
                table: "hierarchy_leafs",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "vendor_sourcing",
                table: "hierarchy_leafs",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }
    }
}
