using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Bonus.Core.Migrations
{
    /// <inheritdoc />
    public partial class AddHieararchyEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "hierarchy_leafs",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    leaf_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    hierarchy_code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    node_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    image_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    sort_order = table.Column<int>(type: "int", nullable: false),
                    type = table.Column<int>(type: "int", nullable: false),
                    is_active = table.Column<bool>(type: "bit", nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false),
                    is_member_club = table.Column<bool>(type: "bit", nullable: false),
                    item_uom = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    member_value = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    deal_price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    prepayment = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    validation_period = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    vendor_sourcing = table.Column<bool>(type: "bit", nullable: false),
                    last_sync_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hierarchy_leafs", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "hierarchy_nodes",
                columns: table => new
                {
                    id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    node_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    hierarchy_code = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    parent_node = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    image_id = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    indentation = table.Column<int>(type: "int", nullable: false),
                    children_order = table.Column<int>(type: "int", nullable: false),
                    presentation_order = table.Column<int>(type: "int", nullable: false),
                    is_deleted = table.Column<bool>(type: "bit", nullable: false),
                    last_sync_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("pk_hierarchy_nodes", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "ix_hierarchy_leafs_leaf_id",
                table: "hierarchy_leafs",
                column: "leaf_id",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "ix_hierarchy_nodes_node_id",
                table: "hierarchy_nodes",
                column: "node_id",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "hierarchy_leafs");

            migrationBuilder.DropTable(
                name: "hierarchy_nodes");
        }
    }
}
